import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_screens/single_mr_screen.dart';
import 'package:connectone/core/bai_widgets/buyer_quotes_dialog.dart';
import 'package:connectone/core/push_notifications/notification_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/options_menu_utility.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/status_list_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../bai_models/summary_res.dart';
import 'seller_quotes_dialog.dart';

class MrGroupingItem extends StatefulWidget {
  const MrGroupingItem({
    Key? key,
    required this.item,
    required this.style,
    required this.refresh,
  }) : super(key: key);

  final Content item;
  final TextStyle style;
  final Function refresh;

  @override
  State<MrGroupingItem> createState() => _MrGroupingItemState();
}

class _MrGroupingItemState extends State<MrGroupingItem> {
  List<Datum>? data;
  bool isLoading = false;

  @override

  /// A widget that displays a MR grouping item. It displays the item name,
  /// site, quantity, created on date, category, and status. It also displays
  /// buttons to quote and open the item. The quote button takes the user to
  /// the buyer or seller offers page depending on the logged in user's role.
  /// The open button takes the user to the notifications page.
  ///
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        children: [
          Container(
            height: 48,
            width: double.infinity,
            padding: const EdgeInsets.only(left: 16),
            decoration: const BoxDecoration(
              color: AppColors.primaryColorOld,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(8),
              ),
            ),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget.item.mvtItemName ?? "N/A",
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(6),
                bottomRight: Radius.circular(8),
              ),
              color: (widget.item.statusCd.toString() == "SPLT")
                  ? getCategoryColor(widget.item.statusCd.toString())
                  : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryColor.withOpacity(0.2),
                  offset: const Offset(0, 2),
                  blurRadius: 6,
                  spreadRadius: 2,
                ),
              ],
            ),
            margin: const EdgeInsets.all(0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          widget.item.orderGroupName.toString(),
                          style: widget.style,
                        ),
                      ),
                      // if (   widget.item.statusCd.toString() == "SPLT"  )Icon(
                      //         Icons.circle,
                      //         color: getCategoryColor(
                      //             widget.item.statusCd.toString()),
                      //         ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildRow("Site Name", widget.item.projectName.toString(),
                      widget.style),
                  const SizedBox(height: 8),
                  _buildRow(
                      "Split Name",
                      widget.item.prchOrdrSplitName?.toString() ?? "N/A",
                      widget.style),
                  const SizedBox(height: 8),
                  _buildRow(
                      "Quantity",
                      widget.item.quantity?.toStringAsFixed(0) ?? "N/A",
                      widget.style),
                  const SizedBox(height: 8),
                  _buildRow("Created on",
                      widget.item.createdAt?.toCreatedOn() ?? '', widget.style),
                  const SizedBox(height: 8),
                  _buildRow(
                      "Delivery on",
                      widget.item.deliveryDate?.toDeliveryOn() ?? '',
                      widget.style),
                  const SizedBox(height: 8),
                  _buildRow("Category",
                      widget.item.cappCategoriesName.toString(), widget.style),
                  const SizedBox(height: 8),
                  _buildRow("Status", widget.item.statusName.toString(),
                      widget.style),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 32,
                          child: OutlinedButton(
                            onPressed: () async {
                              if (getRoleLevel() == 15) {
                                alert(
                                    "For quotes, please contact the manager.");
                                return;
                              }
                              // var date = await showDialog(
                              //     context: context,
                              //     builder: (context) {
                              //       return MRSummaryDialog(
                              //         orderGroupId:
                              //             widget.item.orderGroupId.toString(),
                              //       );
                              //     });
                              // if (date == null) {
                              //   return;
                              // }
                              if (isBuyer()) {
                                showDialog(
                                    context: context,
                                    builder: (context) {
                                      return BuyerQuotesDialog(
                                        prchOrdrId:
                                            widget.item.prchOrdrId?.toInt() ??
                                                0,
                                        brand: "",
                                      );
                                    }).then((val) {
                                  widget.refresh();
                                });

                                // Get.to(BuyerOffersPage(
                                //   item: widget.item,
                                //   showSummary: false,
                                //   selectedIndex: 1,
                                //   deliveryDate: date,
                                // ))?.then((val) {
                                //   widget.refresh();
                                // });
                              } else {
                                var enableSubmit = ["QUOT", "NEGO", "VASS"]
                                    .contains(widget.item.statusCd);
                                if (!enableSubmit) {
                                  properAlertWithOk(
                                    "This MR is currently not accepting offers. Do you want to proceed?",
                                    () {
                                      Navigator.pop(context);
                                      showDialog(
                                          context: context,
                                          builder: (context) {
                                            return SellerQuotesDialog(
                                                statusCd: "QUOT",
                                              prchOrdrId: widget.item.prchOrdrId
                                                      ?.toInt() ??
                                                  0,
                                              itemDetails: Detail(
                                                  quantity: widget.item.quantity
                                                          ?.toInt() ??
                                                      0),
                                            );
                                          }).then((val) {
                                        widget.refresh();
                                      });
                                      // Get.to(SellerOffersPage(
                                      //   item: widget.item,
                                      //   showSummary: false,
                                      //   selectedIndex: 1,
                                      //   deliveryDate: date,
                                      // ))?.then((val) {
                                      //   widget.refresh();
                                      // });
                                    },
                                  );
                                  return;
                                } else {
                                  showDialog(
                                      context: context,
                                      builder: (context) {
                                        return SellerQuotesDialog(
                                            statusCd: "QUOT",
                                          prchOrdrId:
                                              widget.item.prchOrdrId?.toInt() ??
                                                  0,
                                          itemDetails: Detail(
                                              quantity: widget.item.quantity
                                                      ?.toInt() ??
                                                  0),
                                        );
                                      }).then((val) {
                                    widget.refresh();
                                  });
                                  // Get.to(SellerOffersPage(
                                  //   item: widget.item,
                                  //   showSummary: false,
                                  //   selectedIndex: 1,
                                  //   deliveryDate: date,
                                  // ))?.then((val) {
                                  //   widget.refresh();
                                  // });
                                }
                              }
                            },
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(
                                  color: AppColors
                                      .primaryColorOld), // Border color
                            ),
                            child: const Text(
                              'Quote',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryColorOld, // Text color
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: SizedBox(
                          height: 32,
                          child: ElevatedButton(
                            onPressed: () {
                              Get.to(SingleMRScreen(
                                isNotify: false,
                                notificationData: NotificationData(
                                  code: widget.item.mvtItemName.toString(),
                                  subject: widget.item.mvtItemName.toString(),
                                  event: '',
                                  content:
                                      widget.item.cappCategoriesName.toString(),
                                  id: widget.item.prchOrdrId,
                                ),
                              ));
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.green,
                            ),
                            child: const Text(
                              'Open',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: SizedBox(
                          height: 32,
                          child: GestureDetector(
                            onTapDown: (details) {
                              OptionsMenuUtility optionsMenuUtility =
                                  OptionsMenuUtility(
                                context: context,
                                content: widget.item,
                                position: details.globalPosition,
                                prchOrdrId: [
                                  widget.item.prchOrdrId?.toInt() ?? 0
                                ],
                              );
                              optionsMenuUtility.fetchStatusDropdown();
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: AppColors.primaryColorOld,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: isLoading
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    )
                                  : const Text(
                                      'Options',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRow(String label, String value, TextStyle style) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: style,
          ),
        ),
        Text(": ", style: style),
        const SizedBox(width: 6),
        Expanded(
          flex: 4,
          child: Text(
            value,
            style: style,
          ),
        ),
      ],
    );
  }
}
