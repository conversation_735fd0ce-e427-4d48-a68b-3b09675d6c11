import 'dart:developer' as logger;

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:connectone/bai_screens/single_mr_screen.dart';
import 'package:get/get.dart';

class NotificationController {
  @pragma("vm:entry-point")
  static Future<void> onNotificationCreatedMethod(
      ReceivedNotification receivedNotification) async {}

  @pragma("vm:entry-point")
  static Future<void> onNotificationDisplayedMethod(
      ReceivedNotification receivedNotification) async {}

  @pragma("vm:entry-point")
  static Future<void> onDismissActionReceivedMethod(
      ReceivedAction receivedAction) async {}

  @pragma("vm:entry-point")
  static Future<void> onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    try {
      logger.log("Received action: $receivedAction");

      final payload = receivedAction.payload;
      if (payload == null) return;

      final code = payload['code'] ?? '';
      final subject = payload['subject'] ?? '';
      final event = payload['event'] ?? '';
      final content = payload['content'] ?? '';
      final id = payload['id'] ?? '';

      _navigateBasedOnNotification(NotificationData(
        code: code,
        subject: subject,
        event: event,
        content: content,
        id: id,
      ));
    } catch (e) {
      logger.log('Error handling notification action: ${e.toString()}');
    }
  }

  static Future<void> _navigateBasedOnNotification(
      NotificationData data) async {
    await Future.delayed(const Duration(seconds: 3));
    Get.to(SingleMRScreen(notificationData: data,isNotify: true,));
    // switch (notificationCode) {
    //   case 'some_code':
    //     Get.to(() => SellerLiveNotifications());
    //     break;
    //   default:
    //     logger.log('No navigation defined for notification code: $notificationCode');
    //     break;
    // }
  }
}

class NotificationData {
  final String code;
  final String subject;
  final String event;
  final String content;
  final dynamic id;

  NotificationData({
    required this.code,
    required this.subject,
    required this.event,
    required this.content,
    required this.id,
  });
}
