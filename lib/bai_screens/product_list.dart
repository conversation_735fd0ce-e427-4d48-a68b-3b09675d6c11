import 'dart:ui';

import 'package:connectone/bai_blocs/product_list/cubit/product_list_cubit.dart';
import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_screens/add_product.dart';
import 'package:connectone/bai_screens/check_out.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:connectone/bai_models/test_search_res.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';

class ProductList extends StatefulWidget {
  const ProductList({
    Key? key,
    required this.title,
    required this.categoryId,
    this.orderGroupId,
    this.deliveryDate,
    required this.isMr,
    this.splitGroupId,
    this.cappCategoryId,
    this.splitGroupName,
  }) : super(key: key);

  final String title;
  final String categoryId;
  final int? orderGroupId;
  final DateTime? deliveryDate;
  final bool isMr;
  final int? splitGroupId;
  final int? cappCategoryId;
  final String? splitGroupName;

  @override
  State<ProductList> createState() => _ProductListState();
}

class _ProductListState extends State<ProductList> {
  @override
  void initState() {
    context.read<ProductListCubit>().loadProducts(widget.categoryId);
    super.initState();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Browse Products\n\nHere you can view all available products in this category.\n\nEach product shows:\n- Product image\n- Product name\n- Product details\n\nTap on any product to view more details and add it to your cart.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override

  /// Builds the UI for the product list page
  ///
  /// This page is used to display the list of products. The products are
  /// loaded from the server when the page is first loaded. The user can
  /// tap on a product to view its details.
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: AppColors.primaryColor,
        actions: [
          Badge(
            label: Text(BaiCart.cartItems.length.toString()),
            offset: const Offset(-0, 6),
            child: IconButton(
              icon: const Icon(Icons.shopping_cart),
              onPressed: () {
                Get.to(CheckOut(
                  isMr: widget.isMr,
                  deliveryDate: widget.deliveryDate,
                ))?.then((val) {
                  setState(() {});
                }); // Navigates to the checkout screen
              },
            ),
          ),
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<ProductListCubit, ProductListState>(
          listener: (context, state) {},
          builder: (context, state) {
            /// Show or hide the loader depending on the state
            (state is ProductListLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();

            if (state is ProductListLoaded) {
              /// Get the list of products from the state
              var products = state.products;

              /// Return a scrollable list of products
              return SizedBox(
                height: MediaQuery.of(context).size.height - 72,
                child: products.isNotEmpty == true
                    ? ListView.separated(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        itemBuilder: (context, index) {
                          /// Get the product at the current index
                          var item = products[index];

                          /// Return a ListTile for the current product
                          return ListTile(
                            title: Text(
                              /// Display the name of the product
                              item.name ?? "",
                              style: const TextStyle(
                                /// Use bold font for the product name
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            onTap: () {
                              /// Navigate to the product details page when the
                              /// product is tapped
                              // Navigator.pop(context, item);
                              // plr.Content content = item!;
                              var itemNew = Content(
                                /// The ID of the product
                                id: item.id,

                                /// The name of the product
                                name: item.name,
                              );
                              // alert("${widget.categoryId} ${item.service?.id}");
                              // alert((!BaiCart.isCategoryAllowed(
                              //   item.service?.id?.toInt() ?? 0,
                              //   splitCategoryId: widget.cappCategoryId,
                              // )).toString());
                              // return;
                              if (!BaiCart.isCategoryAllowed(
                                item.service?.id?.toInt() ?? 0,
                                splitCategoryId: widget.cappCategoryId,
                              )) {
                                categoryAlert(
                                  message:
                                      "Your cart has items from a different category. Please create an MR with your current items or remove them before adding items from this category.",
                                  onGoToCart: () {
                                    Navigator.pop(context);
                                    Get.to(
                                      () => CheckOut(
                                        isMr: widget.isMr,
                                        deliveryDate: widget.deliveryDate,
                                      ),
                                    );
                                  },
                                );
                                return;
                              }
                              Get.to(AddProductPage(
                                /// Pass the product to the AddProductPage
                                item: itemNew,

                                /// Pass the order group ID to the AddProductPage
                                orderGroupId: widget.orderGroupId,
                                deliveryDate: widget.deliveryDate,
                                isMr: widget.isMr,
                                splitGroupId: widget.splitGroupId,
                                categoryId: widget.cappCategoryId,
                                splitGroupName: widget.splitGroupName,
                              ));
                            },
                            leading: Container(
                              height: 48,
                              width: 48,
                              clipBehavior: Clip.hardEdge,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: BaiImage(
                                /// Display the image of the product
                                url: item.image ?? "",
                              ),
                            ),
                          );
                        },
                        itemCount: products.length,
                        separatorBuilder: (BuildContext context, int index) {
                          return const SizedBox(height: 4);
                        },
                      )
                    : const Center(child: Text("No products found")),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}
