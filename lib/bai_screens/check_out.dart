import 'dart:ui';

import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_screens/submit_enquiry.dart';
import 'package:connectone/core/bai_widgets/add_project_text_form_field.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/cart_item.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../bai_blocs/purchases_bloc/purchases_cubit.dart';
import '../core/bai_widgets/help_info.dart';
import '../core/utils/tools.dart';

class CheckOut extends StatefulWidget {
  const CheckOut({
    Key? key,
    required this.isMr,
    this.deliveryDate,
  }) : super(key: key);

  final bool isMr;
  final DateTime? deliveryDate;

  @override
  State<CheckOut> createState() => _CheckOutState();
}

class _CheckOutState extends State<CheckOut> {
  final bool _includeInbargainBazar = false;
  var dateController = TextEditingController();
  var siteAccessController = TextEditingController();

  String? selectedSiteAccess;

  String? _dateError;
  String? _siteAccessError;
  String? _cartError;

  @override
  void initState() {
    super.initState();
    // Auto-populate delivery date if provided
    if (widget.deliveryDate != null) {
      dateController.text = formatDateToDDMMYYYY(widget.deliveryDate!);
      BaiCart.deliveryDate = widget.deliveryDate;
    }

    if (BaiCart.siteAccessTypeList?.roadAccessTypes
            ?.any((element) => element.value == BaiCart.siteAccess) ??
        false) {
      selectedSiteAccess = BaiCart.siteAccess;
    } else {
      _getSiteAccesses();
    }
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Checkout!\n\nOn this screen you can:\n\n1. Review your material request list\n\n2. Adjust quantities using + and - buttons\n\n3. Remove items using the trash icon\n\n4. Select site access type for delivery\n\n5. Choose your preferred delivery date\n\n6. Optionally include your order in Bargain Bazar\n\nMake sure all required fields are filled before proceeding.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  _getSiteAccesses() {
    context.read<PurchasesCubit>().getUnitsAndSiteAccess();
  }

  void _showDeliveryDateWarning() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delivery Date Warning'),
          content: const Text(
              'You cannot create multiple orders with different delivery dates in a single MR.\nPlease create a new MR to change the delivery date.'),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _validateAndProceed() {
    setState(() {
      _dateError = dateController.text.isEmpty ? 'Please select a date' : null;
      _siteAccessError =
          selectedSiteAccess == null ? 'Please enter site access' : null;
      _cartError = BaiCart.cartItems.isEmpty ? 'Your cart is empty' : null;
    });

    if (_dateError == null && _siteAccessError == null && _cartError == null) {
      Get.to(SubmitEnquiryScreen(
        isMr: widget.isMr,
        deliveryDate: widget.deliveryDate,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          Badge(
            label: Text(BaiCart.cartItems.length.toString()),
            offset: const Offset(-0, 6),
            child: IconButton(
              icon: const Icon(Icons.shopping_cart),
              onPressed: () {
                Get.to(CheckOut(
                  isMr: widget.isMr,
                  deliveryDate: widget.deliveryDate,
                ))?.then((val) {
                  setState(() {});
                }); // Navigates to the checkout screen
              },
            ),
          ),
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
        elevation: 0,
        title: Text('Add ${widget.isMr ? "Material" : "Service"} Request'),
        backgroundColor: AppColors.primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF0F0F0),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Your Material Request List",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      const Row(
                        children: [
                          Expanded(
                            child: Text(
                              "PRODUCT",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(
                            child: Text(
                              "QUANTITY",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          var item = BaiCart.cartItems[index];
                          return CartItem(
                            productName: item.poStockItem?.mvtItemName ?? "-",
                            unit: item.selectedUnit ?? "N/A",
                            quantity: item.poStockItem?.quantity ?? 0,
                            instructions: item.poStockItem?.instructions ?? "-",
                            index: index,
                            item: item,
                            onAdd: () {
                              var currentQuantity = BaiCart
                                      .cartItems[index].poStockItem?.quantity ??
                                  0;
                              if (currentQuantity >= 0) {
                                BaiCart.cartItems[index].poStockItem?.quantity =
                                    currentQuantity + 1;
                                setState(() {});
                              }
                            },
                            onSubtract: () {
                              var currentQuantity = BaiCart
                                      .cartItems[index].poStockItem?.quantity ??
                                  0;
                              if (currentQuantity >= 1) {
                                BaiCart.cartItems[index].poStockItem?.quantity =
                                    currentQuantity - 1;
                                setState(() {});
                              }
                            },
                            onDelete: () {
                              BaiCart.cartItems.removeAt(index);
                              setState(() {});
                            },
                          );
                        },
                        separatorBuilder: (context, index) {
                          return const SizedBox(height: 20);
                        },
                        itemCount: BaiCart.cartItems.length,
                        shrinkWrap: true,
                      ),
                      if (_cartError != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            _cartError!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Site Access',
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Colors.black,
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Colors.black,
                      width: 1,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Colors.black,
                      width: 1,
                    ),
                  ),
                  labelStyle: TextStyle(color: Colors.black),
                  isDense: true,
                ),
                items: BaiCart.siteAccessTypeList?.roadAccessTypes
                    ?.map((site) => DropdownMenuItem<String>(
                          value: site.value,
                          child: Text("${site.value}"),
                        ))
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    selectedSiteAccess = value;
                    BaiCart.siteAccess = value;
                  });
                },
                value: selectedSiteAccess,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Site Access is required';
                  }
                  return null;
                },
              ),
              if (_siteAccessError != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    _siteAccessError!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () {
                  if (widget.deliveryDate != null) {
                    properAlert(
                        "You cannot change delivery date while splitting the MR.");
                  } else {
                    _showDatePicker();
                  }
                  // }
                },
                child: AbsorbPointer(
                  child: AddProjectTextFormField(
                    labelText: "Choose Delivery Date",
                    controller: dateController,
                  ),
                ),
              ),
              if (_dateError != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    _dateError!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Expanded(
                    child: Text("Include this order in the Bargain Bazar"),
                  ),
                  Checkbox(
                    value: _includeInbargainBazar,
                    onChanged: (_) {
                      // Commented out as per original code
                      // setState(() {
                      //   _includeInbargainBazar = !_includeInbargainBazar;
                      // });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 20),
              BaiButton(
                onTap: _validateAndProceed,
                text: "NEXT > CONTACT DETAILS",
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showDatePicker() async {
    var date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2028),
    );
    if (date != null) {
      setState(() {
        dateController.text = formatDateToDDMMYYYY(date);
        BaiCart.deliveryDate = date;
      });
    }
  }

  String formatDateToDDMMYYYY(DateTime dateTime) {
    final DateFormat formatter = DateFormat('dd-MM-yyyy');
    return formatter.format(dateTime);
  }
}
